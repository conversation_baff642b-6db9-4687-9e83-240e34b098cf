import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
// import 'package:image/image.dart' as img; // تم إزالة المكتبة لتقليل الحجم
import 'package:path/path.dart' as path;
import '../utils/logger.dart';

/// خدمة محسنة لإدارة رفع وتحميل الملفات مع Firebase Storage
class FileStorageService {
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // مسارات التخزين
  static const String _profilePath = 'profile_images';
  static const String _attachmentsPath = 'attachments';

  // إعدادات الضغط (تم تبسيطها)
  // static const int _maxImageWidth = 1920; // غير مستخدم
  // static const int _maxImageHeight = 1080; // غير مستخدم
  // static const int _imageQuality = 85; // غير مستخدم
  static const int _maxFileSize = 10 * 1024 * 1024; // 10MB

  /// رفع صورة مع ضغط تلقائي
  static Future<String?> uploadImage({
    required File imageFile,
    required String category,
    String? customPath,
    Function(double)? onProgress,
    bool compress = true,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // التحقق من حجم الملف
      final fileSize = await imageFile.length();
      if (fileSize > _maxFileSize) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
      }

      // ضغط الصورة إذا كان مطلوباً
      File processedFile = imageFile;
      if (compress) {
        processedFile = await _compressImage(imageFile);
      }

      // إنشاء مسار الملف
      final fileName = _generateFileName(imageFile.path, 'jpg');
      final storagePath = customPath ?? '$category/$fileName';

      // رفع الملف
      final ref = _storage.ref().child(storagePath);
      final metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'uploadedBy': user.uid,
          'uploadedAt': DateTime.now().toIso8601String(),
          'originalName': path.basename(imageFile.path),
          'category': category,
        },
      );

      final uploadTask = ref.putFile(processedFile, metadata);

      // تتبع التقدم
      uploadTask.snapshotEvents.listen((snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      AppLogger.success(
        'تم رفع الصورة بنجاح: $downloadUrl',
        'FileStorageService',
      );
      return downloadUrl;
    } catch (e) {
      AppLogger.error('خطأ في رفع الصورة', 'FileStorageService', e);
      return null;
    }
  }

  /// رفع ملف مرفق
  static Future<String?> uploadAttachment({
    required File file,
    String? customPath,
    Function(double)? onProgress,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // التحقق من حجم الملف
      final fileSize = await file.length();
      if (fileSize > _maxFileSize) {
        throw Exception('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
      }

      // التحقق من نوع الملف
      final extension = path.extension(file.path).toLowerCase();
      if (!_isAllowedFileType(extension)) {
        throw Exception(
          'نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX',
        );
      }

      // إنشاء مسار الملف
      final fileName = _generateFileName(file.path, extension.substring(1));
      final storagePath = customPath ?? '$_attachmentsPath/$fileName';

      // رفع الملف
      final ref = _storage.ref().child(storagePath);
      final metadata = SettableMetadata(
        contentType: _getContentType(extension),
        customMetadata: {
          'uploadedBy': user.uid,
          'uploadedAt': DateTime.now().toIso8601String(),
          'originalName': path.basename(file.path),
          'fileSize': fileSize.toString(),
          'fileExtension': extension,
        },
      );

      final uploadTask = ref.putFile(file, metadata);

      // تتبع التقدم
      uploadTask.snapshotEvents.listen((snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      AppLogger.success(
        'تم رفع الملف بنجاح: $downloadUrl',
        'FileStorageService',
      );
      return downloadUrl;
    } catch (e) {
      AppLogger.error('خطأ في رفع الملف', 'FileStorageService', e);
      return null;
    }
  }

  /// رفع صورة شخصية
  static Future<String?> uploadProfileImage({
    required File imageFile,
    Function(double)? onProgress,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // ضغط الصورة للصورة الشخصية (مبسط)
      final compressedFile = await _compressImage(imageFile);

      // إنشاء مسار الملف
      final fileName =
          'profile_${user.uid}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final storagePath = '$_profilePath/${user.uid}/$fileName';

      // حذف الصورة الشخصية السابقة إن وجدت
      await _deleteOldProfileImage(user.uid);

      // رفع الملف
      final ref = _storage.ref().child(storagePath);
      final metadata = SettableMetadata(
        contentType: 'image/jpeg',
        customMetadata: {
          'uploadedBy': user.uid,
          'uploadedAt': DateTime.now().toIso8601String(),
          'isProfileImage': 'true',
        },
      );

      final uploadTask = ref.putFile(compressedFile, metadata);

      // تتبع التقدم
      uploadTask.snapshotEvents.listen((snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        onProgress?.call(progress);
      });

      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      AppLogger.success('تم رفع الصورة الشخصية بنجاح', 'FileStorageService');
      return downloadUrl;
    } catch (e) {
      AppLogger.error('خطأ في رفع الصورة الشخصية', 'FileStorageService', e);
      return null;
    }
  }

  /// حذف ملف من التخزين
  static Future<bool> deleteFile(String downloadUrl) async {
    try {
      final ref = _storage.refFromURL(downloadUrl);
      await ref.delete();
      AppLogger.success('تم حذف الملف بنجاح', 'FileStorageService');
      return true;
    } catch (e) {
      AppLogger.error('خطأ في حذف الملف', 'FileStorageService', e);
      return false;
    }
  }

  /// ضغط الصورة (مبسط)
  static Future<File> _compressImage(File imageFile) async {
    try {
      // للآن نرجع الملف الأصلي - تم إزالة مكتبة image لتوفير مساحة APK
      // يمكن إضافة ضغط بسيط لاحقاً إذا احتجنا
      AppLogger.info(
        'تم تخطي ضغط الصورة لتوفير مساحة APK',
        'FileStorageService',
      );
      return imageFile;
    } catch (e) {
      AppLogger.error('خطأ في معالجة الصورة', 'FileStorageService', e);
      return imageFile;
    }
  }

  /// حذف الصورة الشخصية السابقة
  static Future<void> _deleteOldProfileImage(String userId) async {
    try {
      final listResult = await _storage.ref('$_profilePath/$userId').listAll();
      for (final item in listResult.items) {
        await item.delete();
      }
    } catch (e) {
      // تجاهل الأخطاء - قد لا توجد صورة سابقة
    }
  }

  /// إنشاء اسم ملف فريد
  static String _generateFileName(String originalPath, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomSuffix = DateTime.now().microsecond;
    return '${timestamp}_$randomSuffix.$extension';
  }

  /// التحقق من نوع الملف المسموح
  static bool _isAllowedFileType(String extension) {
    const allowedTypes = [
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.ppt',
      '.pptx',
    ];
    return allowedTypes.contains(extension.toLowerCase());
  }

  /// الحصول على نوع المحتوى
  static String _getContentType(String extension) {
    switch (extension.toLowerCase()) {
      case '.pdf':
        return 'application/pdf';
      case '.doc':
        return 'application/msword';
      case '.docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case '.xls':
        return 'application/vnd.ms-excel';
      case '.xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case '.ppt':
        return 'application/vnd.ms-powerpoint';
      case '.pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      default:
        return 'application/octet-stream';
    }
  }

  /// الحصول على معلومات الملف
  static Future<Map<String, dynamic>?> getFileMetadata(
    String downloadUrl,
  ) async {
    try {
      final ref = _storage.refFromURL(downloadUrl);
      final metadata = await ref.getMetadata();
      return {
        'name': metadata.name,
        'size': metadata.size,
        'contentType': metadata.contentType,
        'timeCreated': metadata.timeCreated,
        'updated': metadata.updated,
        'customMetadata': metadata.customMetadata,
      };
    } catch (e) {
      AppLogger.error(
        'خطأ في الحصول على معلومات الملف',
        'FileStorageService',
        e,
      );
      return null;
    }
  }
}
