@echo off
echo ========================================
echo      تحليل حجم APK وتقرير مفصل
echo ========================================
echo.

echo 1. تحليل حجم APK...
call flutter build apk --analyze-size --target-platform android-arm64
echo   ✅ تم تحليل الحجم

echo.
echo 2. إنشاء تقرير Bundle...
call flutter build appbundle --analyze-size
echo   ✅ تم إنشاء تقرير Bundle

echo.
echo 3. فحص التبعيات غير المستخدمة...
call flutter pub deps --style=compact
echo   ✅ تم فحص التبعيات

echo.
echo 4. تحليل الكود...
call flutter analyze --no-fatal-infos
echo   ✅ تم تحليل الكود

echo.
echo ========================================
echo           تقرير الأحجام
echo ========================================
echo.

if exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo APK عادي:
    for %%I in ("build\app\outputs\flutter-apk\app-release.apk") do echo   الحجم: %%~zI bytes
    echo.
)

if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release.apk" (
    echo APK محسن ARM64:
    for %%I in ("build\app\outputs\flutter-apk\app-arm64-v8a-release.apk") do echo   الحجم: %%~zI bytes
    echo.
)

if exist "build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk" (
    echo APK محسن ARM32:
    for %%I in ("build\app\outputs\flutter-apk\app-armeabi-v7a-release.apk") do echo   الحجم: %%~zI bytes
    echo.
)

if exist "build\app\outputs\bundle\release\app-release.aab" (
    echo App Bundle:
    for %%I in ("build\app\outputs\bundle\release\app-release.aab") do echo   الحجم: %%~zI bytes
    echo.
)

echo ========================================
echo        نصائح لتقليل الحجم أكثر
echo ========================================
echo.
echo 1. استخدم App Bundle بدلاً من APK للنشر
echo 2. فعل R8 و ProGuard (تم تفعيلهما)
echo 3. استخدم --split-per-abi للتقسيم حسب المعمارية
echo 4. راجع التبعيات غير المستخدمة
echo 5. ضغط الصور والأصول
echo.
pause
