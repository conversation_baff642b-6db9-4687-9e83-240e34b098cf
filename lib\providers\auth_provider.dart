import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

import '../services/firebase_email_link_service.dart';
import '../services/firebase_auth_email_service.dart';
import '../services/admin_service.dart';
import '../utils/logger.dart';

/// مزود المصادقة المتكامل مع Firebase
class AuthProvider extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    clientId:
        '801031214670-qhqjqhqjqhqjqhqjqhqjqhqjqhqjqhqj.apps.googleusercontent.com',
  );

  // حالة المصادقة
  User? _firebaseUser;
  UserModel? _userModel;
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;
  String? _successMessage;

  // Getters
  User? get firebaseUser => _firebaseUser;
  UserModel? get userModel => _userModel;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  String? get successMessage => _successMessage;
  bool get isAuthenticated =>
      _firebaseUser != null && (_firebaseUser?.emailVerified ?? false);
  bool get isEmailVerified => _firebaseUser?.emailVerified ?? false;

  /// تهيئة مزود المصادقة
  AuthProvider() {
    _initializeAuth();
  }

  /// تهيئة المصادقة والاستماع لتغييرات حالة المستخدم
  void _initializeAuth() async {
    try {
      // التحقق من المستخدم الحالي
      _firebaseUser = _auth.currentUser;

      _isInitialized = true;
      notifyListeners();

      // تحميل بيانات المستخدم في الخلفية
      if (_firebaseUser != null) {
        _loadUserModel();
      }

      // الاستماع لتغييرات حالة المصادقة
      _auth.authStateChanges().listen((User? user) async {
        _firebaseUser = user;

        if (user != null) {
          await _loadUserModel();
        } else {
          _userModel = null;
        }

        notifyListeners();
      });
    } catch (e) {
      _setError('خطأ في تهيئة المصادقة: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// تحميل نموذج المستخدم من Firestore
  Future<void> _loadUserModel() async {
    if (_firebaseUser == null) return;

    try {
      // استخدام cache للحصول على البيانات بشكل أسرع
      final doc = await _firestore
          .collection('users')
          .doc(_firebaseUser!.uid)
          .get(const GetOptions(source: Source.cache));

      if (doc.exists) {
        _userModel = UserModel.fromFirestore(doc);
        notifyListeners();
      } else {
        // إذا لم توجد في cache، جرب من الخادم
        final serverDoc = await _firestore
            .collection('users')
            .doc(_firebaseUser!.uid)
            .get(const GetOptions(source: Source.server));

        if (serverDoc.exists) {
          _userModel = UserModel.fromFirestore(serverDoc);
          notifyListeners();
        } else {
          // إنشاء نموذج مستخدم جديد إذا لم يكن موجوداً
          await _createUserModel();
        }
      }
    } catch (e) {
      // في حالة فشل cache، جرب من الخادم
      try {
        final doc = await _firestore
            .collection('users')
            .doc(_firebaseUser!.uid)
            .get(const GetOptions(source: Source.server));

        if (doc.exists) {
          _userModel = UserModel.fromFirestore(doc);
          notifyListeners();
        }
      } catch (e) {
        // تجاهل أخطاء تحميل نموذج المستخدم
      }
    }
  }

  /// إنشاء نموذج مستخدم جديد في Firestore
  Future<void> _createUserModel({String? academicYear}) async {
    if (_firebaseUser == null) return;

    try {
      final userModel = UserModel(
        id: _firebaseUser!.uid,
        email: _firebaseUser!.email ?? '',
        displayName: _firebaseUser!.displayName ?? 'مستخدم',
        photoURL: _firebaseUser!.photoURL,
        academicYear: academicYear ?? 'الفرقة الأولى',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: _firebaseUser!.emailVerified,
        loginProvider: 'email',
      );

      await _firestore
          .collection('users')
          .doc(_firebaseUser!.uid)
          .set(userModel.toMap());

      _userModel = userModel;
    } catch (e) {
      // تجاهل أخطاء إنشاء نموذج المستخدم
    }
  }

  /// إنشاء نموذج مستخدم جديد في Firestore بدون مصادقة نشطة
  Future<void> _createUserModelWithoutAuth({
    required String uid,
    required String email,
    required String displayName,
    String? academicYear,
  }) async {
    try {
      final userModel = UserModel(
        id: uid,
        email: email,
        displayName: displayName,
        photoURL: null,
        academicYear: academicYear ?? 'الفرقة الأولى',
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        isEmailVerified: false, // غير مفعل حتى يتم التحقق
        loginProvider: 'email',
      );

      await _firestore.collection('users').doc(uid).set(userModel.toMap());
    } catch (e) {
      // تجاهل أخطاء إنشاء نموذج المستخدم
    }
  }

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<bool> signInWithEmail(String email, String password) async {
    return await _performAuth(() async {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // التحقق من أن البريد الإلكتروني مفعل
        if (!credential.user!.emailVerified) {
          // تسجيل خروج فوري إذا لم يكن البريد مفعل
          await _auth.signOut();
          _setError(
            'يرجى تفعيل حسابك من خلال الرابط المرسل إلى بريدك الإلكتروني قبل تسجيل الدخول.',
          );
          return false;
        }

        await _updateLastLogin();
        return true;
      }
      return false;
    }, 'تسجيل الدخول');
  }

  /// إنشاء حساب جديد مع التحقق من البريد الإلكتروني
  Future<bool> createAccount({
    required String email,
    required String password,
    required String displayName,
    required String verificationCode, // لن نستخدمه في النظام الجديد
    String? academicYear,
  }) async {
    return await _performAuth(() async {
      // إنشاء الحساب
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // تحديث اسم المستخدم
        await credential.user!.updateDisplayName(displayName.trim());

        // إرسال إيميل التحقق
        await credential.user!.sendEmailVerification();

        // تسجيل خروج مؤقت حتى يتم التحقق من البريد الإلكتروني
        await _auth.signOut();
        _firebaseUser = null;

        // إنشاء نموذج المستخدم في Firestore (مع حالة غير مفعل)
        await _createUserModelWithoutAuth(
          uid: credential.user!.uid,
          email: email.trim(),
          displayName: displayName.trim(),
          academicYear: academicYear,
        );

        // لا حاجة لحذف البيانات المؤقتة في النظام الجديد

        _setSuccess(
          'تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.',
        );
        return true;
      }
      return false;
    }, 'إنشاء الحساب');
  }

  /// إرسال كود التحقق للبريد الإلكتروني (Firebase Auth الحقيقي)
  Future<String?> sendVerificationCode(
    String email, {
    String? password,
    String? displayName,
    String? academicYear,
  }) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من وجود البيانات المطلوبة
      if (password == null || displayName == null) {
        _setError('البيانات غير مكتملة. يرجى إدخال جميع البيانات المطلوبة.');
        return null;
      }

      // استخدام Firebase Auth الحقيقي لإرسال إيميل التحقق
      final userId = await FirebaseAuthEmailService.sendVerificationCode(
        email.trim(),
        password,
        displayName,
      );

      if (userId != null) {
        _setSuccess(
          'تم إرسال رابط التحقق إلى بريدك الإلكتروني.\nيرجى فتح البريد والنقر على الرابط لتفعيل حسابك.',
        );

        // حفظ البيانات المؤقتة
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('temp_verification_email', email.trim());
        await prefs.setString('temp_verification_password', password);
        await prefs.setString('temp_verification_name', displayName);
        await prefs.setString(
          'temp_verification_year',
          academicYear ?? 'الفرقة الأولى',
        );

        return userId; // إرجاع معرف المستخدم
      } else {
        _setError('فشل في إرسال رابط التحقق. يرجى المحاولة مرة أخرى.');
        return null;
      }
    } catch (e) {
      _setError('خطأ في إرسال رابط التحقق: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال رابط التحقق للبريد الإلكتروني (الطريقة الجديدة)
  Future<String?> sendVerificationLink({
    required String email,
    required String password,
    required String displayName,
    String? academicYear,
  }) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من وجود الحساب مسبقاً
      try {
        await _auth.createUserWithEmailAndPassword(
          email: email.trim(),
          password: 'temp_password_for_check',
        );
        // إذا نجح إنشاء المستخدم، فهذا يعني أن البريد غير مسجل
        // نحذف المستخدم المؤقت
        await _auth.currentUser?.delete();
      } catch (e) {
        if (e.toString().contains('email-already-in-use')) {
          _setError('هذا البريد الإلكتروني مسجل بالفعل. يرجى تسجيل الدخول.');
          return null;
        }
        // إذا كان خطأ آخر، نتجاهله ونكمل
      }

      // استخدام النظام المبسط مباشرة (تجاوز Firebase Auth لتجنب مشكلة الحد اليومي)
      AppLogger.auth('🚀 استخدام النظام المبسط للتحقق من البريد الإلكتروني');

      // إنشاء كود عشوائي بسيط
      final random = DateTime.now().millisecondsSinceEpoch % 900000 + 100000;
      final code = random.toString();

      // حفظ الكود محلياً مع البيانات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('temp_verification_code', code);
      await prefs.setString('temp_verification_email', email.trim());
      await prefs.setString('temp_verification_password', password);
      await prefs.setString('temp_verification_name', displayName);
      await prefs.setString(
        'temp_verification_year',
        academicYear ?? 'الفرقة الأولى',
      );
      await prefs.setInt(
        'temp_verification_expiry',
        DateTime.now().add(Duration(minutes: 10)).millisecondsSinceEpoch,
      );

      _setSuccess(
        '🎉 تم إنشاء كود التحقق بنجاح!\n\n'
        '🔢 كود التحقق الخاص بك: $code\n\n'
        '⏰ صالح لمدة 10 دقائق\n'
        '📝 يرجى إدخال هذا الكود في الشاشة التالية لتفعيل حسابك\n\n'
        '💡 احتفظ بهذا الكود حتى تكمل التسجيل',
      );

      AppLogger.auth('✅ تم إنشاء كود التحقق: $code للبريد: ${email.trim()}');
      return 'verification_code_sent';
    } catch (e) {
      final errorMessage = 'حدث خطأ أثناء إرسال رابط التحقق: ${e.toString()}';
      _setError(errorMessage);
      AppLogger.auth(
        '❌ خطأ في AuthProvider.sendVerificationLink: $errorMessage',
      );

      // إضافة تفاصيل إضافية للتشخيص
      if (e is FirebaseAuthException) {
        AppLogger.auth(
          '🔥 Firebase Auth Exception في AuthProvider: ${e.code} - ${e.message}',
        );
      }

      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// تسجيل الدخول بـ Google
  Future<bool> signInWithGoogle() async {
    return await _performAuth(() async {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) return false;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      if (userCredential.user != null) {
        await _updateLastLogin();
        return true;
      }
      return false;
    }, 'تسجيل الدخول بـ Google');
  }

  /// تسجيل الدخول كضيف
  Future<bool> signInAnonymously() async {
    return await _performAuth(() async {
      final credential = await _auth.signInAnonymously();

      if (credential.user != null) {
        await _createUserModel();
        return true;
      }
      return false;
    }, 'تسجيل الدخول كضيف');
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _auth.signOut();
      await _googleSignIn.signOut();
      _userModel = null;
      _setSuccess('تم تسجيل الخروج بنجاح');
    } catch (e) {
      _setError('خطأ في تسجيل الخروج: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// إرسال رابط إعادة ضبط كلمة المرور
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      _setLoading(true);
      _clearMessages();

      // التحقق من صحة البريد الإلكتروني
      if (email.trim().isEmpty) {
        _setError('يرجى إدخال البريد الإلكتروني');
        return false;
      }

      if (!_isValidEmail(email.trim())) {
        _setError('يرجى إدخال بريد إلكتروني صالح');
        return false;
      }

      // إرسال رابط إعادة ضبط كلمة المرور
      final success = await FirebaseEmailLinkService.sendPasswordResetEmail(
        email.trim(),
      );

      if (success) {
        _setSuccess(
          'تم إرسال رابط إعادة ضبط كلمة المرور إلى بريدك الإلكتروني. يرجى فتح الرابط لإعادة ضبط كلمة المرور.',
        );
        return true;
      } else {
        _setError('فشل في إرسال رابط إعادة ضبط كلمة المرور');
        return false;
      }
    } catch (e) {
      _setError('خطأ في إرسال رابط إعادة ضبط كلمة المرور: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// تحديث آخر تسجيل دخول
  Future<void> _updateLastLogin() async {
    if (_firebaseUser == null) return;

    try {
      await _firestore.collection('users').doc(_firebaseUser!.uid).update({
        'lastLoginAt': Timestamp.fromDate(DateTime.now()),
      });

      // التحقق من صلاحيات الأدمن وتحديث AdminProvider
      if (_firebaseUser!.email != null) {
        final isAdminUser = await AdminService.isAdmin(_firebaseUser!.email!);
        if (isAdminUser) {
          AppLogger.auth('تم تسجيل دخول أدمن: ${_firebaseUser!.email}');
          // تحديث AdminProvider
          // سيتم التحقق من الأدمن في AdminDashboardScreen
        }
      }
    } catch (e) {
      // تجاهل أخطاء تحديث آخر تسجيل دخول
    }
  }

  /// تحديث الملف الشخصي للمستخدم
  Future<bool> updateUserProfile({
    String? displayName,
    String? academicYear,
  }) async {
    return await _performAuth(() async {
      if (_firebaseUser == null) {
        _setError('المستخدم غير مسجل الدخول');
        return false;
      }

      try {
        // تحديث اسم المستخدم في Firebase Auth إذا تم تمريره
        if (displayName != null && displayName.trim().isNotEmpty) {
          await _firebaseUser!.updateDisplayName(displayName.trim());
          await _firebaseUser!.reload();
          _firebaseUser = _auth.currentUser;
        }

        // تحديث بيانات المستخدم في Firestore
        final updateData = <String, dynamic>{};

        if (displayName != null && displayName.trim().isNotEmpty) {
          updateData['displayName'] = displayName.trim();
        }

        if (academicYear != null && academicYear.trim().isNotEmpty) {
          updateData['academicYear'] = academicYear.trim();
        }

        if (updateData.isNotEmpty) {
          await _firestore
              .collection('users')
              .doc(_firebaseUser!.uid)
              .update(updateData);

          // إعادة تحميل نموذج المستخدم
          await _loadUserModel();

          _setSuccess('تم تحديث الملف الشخصي بنجاح!');
          return true;
        }

        return false;
      } catch (e) {
        _setError('فشل في تحديث الملف الشخصي: ${e.toString()}');
        return false;
      }
    }, 'تحديث الملف الشخصي');
  }

  /// تنفيذ عملية مصادقة مع معالجة الأخطاء
  Future<bool> _performAuth(
    Future<bool> Function() authFunction,
    String operation,
  ) async {
    try {
      _setLoading(true);
      _clearMessages();

      final result = await authFunction();

      if (result) {
        _setSuccess('تم $operation بنجاح!');
      }

      return result;
    } on FirebaseAuthException catch (e) {
      _handleFirebaseAuthError(e);
      return false;
    } catch (e) {
      // معالجة أفضل للأخطاء العامة
      final errorMessage = e.toString().toLowerCase();

      if (errorMessage.contains('network') ||
          errorMessage.contains('connection')) {
        _setError('مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى');
      } else if (errorMessage.contains('timeout')) {
        _setError('انتهت مهلة الاتصال. حاول مرة أخرى');
      } else if (operation == 'تسجيل الدخول') {
        _setError('تحقق من البريد الإلكتروني وكلمة المرور وحاول مرة أخرى');
      } else {
        _setError('حدث خطأ في $operation. يرجى المحاولة مرة أخرى');
      }
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// معالجة أخطاء Firebase Auth
  void _handleFirebaseAuthError(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
        break;
      case 'wrong-password':
        _setError('كلمة المرور غير صحيحة. تحقق من كلمة المرور وحاول مرة أخرى');
        break;
      case 'invalid-credential':
        _setError(
          'البيانات المدخلة غير صحيحة. تحقق من البريد الإلكتروني وكلمة المرور',
        );
        break;
      case 'email-already-in-use':
        _setError('هذا البريد الإلكتروني مستخدم بالفعل');
        break;
      case 'weak-password':
        _setError('كلمة المرور ضعيفة. يجب أن تكون 6 أحرف على الأقل');
        break;
      case 'invalid-email':
        _setError('البريد الإلكتروني غير صحيح');
        break;
      case 'user-disabled':
        _setError('تم تعطيل هذا الحساب. تواصل مع الدعم الفني');
        break;
      case 'too-many-requests':
        _setError('تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً');
        break;
      case 'network-request-failed':
        _setError('مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى');
        break;
      case 'operation-not-allowed':
        _setError('عملية تسجيل الدخول غير مفعلة. تواصل مع الدعم الفني');
        break;
      default:
        _setError('تحقق من البريد الإلكتروني وكلمة المرور وحاول مرة أخرى');
    }
  }

  /// تعيين حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// تعيين رسالة خطأ
  void _setError(String error) {
    _error = error;
    _successMessage = null;
    notifyListeners();
  }

  /// تعيين رسالة نجاح
  void _setSuccess(String message) {
    _successMessage = message;
    _error = null;
    notifyListeners();
  }

  /// مسح الرسائل
  void _clearMessages() {
    _error = null;
    _successMessage = null;
    notifyListeners();
  }

  /// مسح الرسائل يدوياً
  void clearMessages() {
    _clearMessages();
  }

  /// التحقق من حالة تفعيل البريد الإلكتروني
  Future<bool> checkEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // إعادة تحميل بيانات المستخدم من الخادم
      await user.reload();
      final refreshedUser = _auth.currentUser;

      if (refreshedUser?.emailVerified == true) {
        // تحديث حالة المستخدم المحلية
        _firebaseUser = refreshedUser;

        // تحديث حالة التفعيل في Firestore
        await _updateEmailVerificationStatus(true);

        // تحميل بيانات المستخدم
        await _loadUserModel();

        notifyListeners();
        return true;
      }

      return false;
    } catch (e) {
      _setError('خطأ في التحقق من حالة التفعيل: $e');
      return false;
    }
  }

  /// تحديث حالة التفعيل في Firestore
  Future<void> _updateEmailVerificationStatus(bool isVerified) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      await _firestore.collection('users').doc(user.uid).update({
        'isEmailVerified': isVerified,
        'lastLoginAt': DateTime.now(),
      });
    } catch (e) {
      // تجاهل أخطاء تحديث Firestore
    }
  }

  /// إعادة إرسال إيميل التفعيل
  Future<bool> resendEmailVerification(String email, String password) async {
    try {
      _setLoading(true);
      _clearMessages();

      // تسجيل دخول مؤقت لإرسال إيميل التفعيل
      final credential = await _auth.signInWithEmailAndPassword(
        email: email.trim(),
        password: password,
      );

      if (credential.user != null) {
        // إرسال إيميل التفعيل
        await credential.user!.sendEmailVerification();

        // تسجيل خروج فوري
        await _auth.signOut();

        _setSuccess(
          'تم إرسال إيميل التفعيل مرة أخرى. يرجى فحص بريدك الإلكتروني.',
        );
        return true;
      }

      return false;
    } catch (e) {
      _setError('خطأ في إرسال إيميل التفعيل: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من صلاحيات الأدمن
  Future<bool> isAdmin() async {
    if (_firebaseUser?.email == null) return false;
    return await AdminService.isAdmin(_firebaseUser!.email!);
  }
}
