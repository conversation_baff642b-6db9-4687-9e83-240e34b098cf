import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/enhanced_chat_message.dart';
// import 'reply_animations.dart'; // تم حذف الملف لتقليل الحجم

/// Widget لسحب الرسالة للرد عليها مثل WhatsApp
class SwipeToReplyWidget extends StatefulWidget {
  final Widget child;
  final EnhancedChatMessage message;
  final VoidCallback onReply;
  final bool isFromCurrentUser;
  final Color? replyIconColor;
  final double swipeThreshold;
  final Function(EnhancedChatMessage, Offset)? onLongPress;

  const SwipeToReplyWidget({
    super.key,
    required this.child,
    required this.message,
    required this.onReply,
    required this.isFromCurrentUser,
    this.replyIconColor,
    this.swipeThreshold = 0.1, // حد منخفض جداً للاختبار - 10% فقط
    this.onLongPress,
  });

  @override
  State<SwipeToReplyWidget> createState() => _SwipeToReplyWidgetState();
}

class _SwipeToReplyWidgetState extends State<SwipeToReplyWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  double _dragDistance = 0.0;
  bool _isDragging = false;
  bool _hasTriggered = false;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));
  }

  @override
  void dispose() {
    _slideController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _handlePanStart(DragStartDetails details) {
    if (kDebugMode) {
      print('🎯 بدء السحب - isFromCurrentUser: ${widget.isFromCurrentUser}');
    }

    setState(() {
      _isDragging = true;
      _hasTriggered = false;
      _dragDistance = 0.0;
    });

    _fadeController.forward();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    final deltaX = details.delta.dx;
    final screenWidth = MediaQuery.of(context).size.width;

    if (kDebugMode) {
      print(
        '📱 تحديث السحب: deltaX=$deltaX, isFromCurrentUser=${widget.isFromCurrentUser}',
      );
    }

    // منطق السحب المبسط - أي اتجاه مسموح للاختبار
    setState(() {
      _dragDistance += deltaX;

      // حد أقصى للمسافة
      final maxDistance = screenWidth * 0.5;
      _dragDistance = _dragDistance.clamp(-maxDistance, maxDistance);
    });

    // حساب التقدم
    final progress = (_dragDistance.abs() /
            (screenWidth * widget.swipeThreshold))
        .clamp(0.0, 1.0);

    // تحديث الانيميشن
    _scaleController.value = progress;
    _fadeController.value = progress.clamp(0.3, 1.0);

    if (kDebugMode) {
      print(
        '📏 المسافة: $_dragDistance, التقدم: ${(progress * 100).toStringAsFixed(1)}%',
      );
    }

    // تفعيل الرد عند الوصول للحد
    if (progress >= 1.0 && !_hasTriggered) {
      _hasTriggered = true;
      if (kDebugMode) {
        print('🎉 تم تفعيل الرد!');
      }
      _triggerReply();
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (!_isDragging) return;

    _isDragging = false;

    // إعادة تعيين الموضع
    _slideController.reverse();
    _scaleController.reverse();
    _fadeController.reverse();

    setState(() {
      _dragDistance = 0.0;
    });
  }

  /// التعامل مع النقر للرد
  void _handleTap() {
    if (kDebugMode) {
      print('👆 نقر للرد على الرسالة: ${widget.message.content}');
    }

    // تأثير بصري سريع
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    // اهتزاز خفيف (مبسط)
    // ReplyAnimations.triggerReplyHaptic(); // تم تعطيله لتقليل الحجم

    // تفعيل الرد
    widget.onReply();
  }

  void _triggerReply() {
    // اهتزاز متقدم (مبسط)
    // ReplyAnimations.triggerReplyHaptic(); // تم تعطيله لتقليل الحجم

    // تأثير بصري
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    // استدعاء دالة الرد
    widget.onReply();
  }

  /// التعامل مع الضغط المطول
  void _handleLongPress(LongPressStartDetails details) {
    if (widget.onLongPress != null) {
      final RenderBox renderBox = context.findRenderObject() as RenderBox;
      final position = renderBox.globalToLocal(details.globalPosition);
      widget.onLongPress!(widget.message, position);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: GestureDetector(
        // تحسين كشف السحب والنقر
        behavior: HitTestBehavior.translucent,
        onHorizontalDragStart: _handlePanStart,
        onHorizontalDragUpdate: _handlePanUpdate,
        onHorizontalDragEnd: _handlePanEnd,
        // إضافة النقر للرد
        onTap: _handleTap,
        // إضافة الضغط المطول
        onLongPressStart: widget.onLongPress != null ? _handleLongPress : null,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // أيقونة الرد في الخلفية - دائماً مرئية للاختبار
            Positioned.fill(
              child: Container(
                color: Colors.transparent,
                child: AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value.clamp(
                        0.3,
                        1.0,
                      ), // حد أدنى للرؤية
                      child: _buildReplyIcon(),
                    );
                  },
                ),
              ),
            ),

            // تأثير الضوء عند السحب (مبسط)
            if (_dragDistance.abs() > 0)
              Container(
                width:
                    (_dragDistance.abs() /
                            (MediaQuery.of(context).size.width *
                                widget.swipeThreshold))
                        .clamp(0.0, 1.0) *
                    50,
                height: 2,
                color: widget.replyIconColor ?? const Color(0xFF6366F1),
              ),

            // الرسالة القابلة للسحب
            Transform.translate(
              offset: Offset(_dragDistance, 0.0),
              child: SizedBox(width: double.infinity, child: widget.child),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReplyIcon() {
    return Align(
      alignment:
          widget.isFromCurrentUser
              ? Alignment.centerRight
              : Alignment.centerLeft,
      child: Padding(
        padding: EdgeInsets.only(
          left: widget.isFromCurrentUser ? 0 : 16,
          right: widget.isFromCurrentUser ? 16 : 0,
        ),
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: 0.7 + (_scaleAnimation.value * 0.6),
              child: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: widget.replyIconColor ?? const Color(0xFF6366F1),
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: [
                    BoxShadow(
                      color: (widget.replyIconColor ?? const Color(0xFF6366F1))
                          .withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.reply_rounded,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Widget مبسط لعرض معاينة الرد
class ReplyPreviewWidget extends StatelessWidget {
  final EnhancedChatMessage replyToMessage;
  final VoidCallback? onCancel;
  final bool isDarkMode;

  const ReplyPreviewWidget({
    super.key,
    required this.replyToMessage,
    this.onCancel,
    this.isDarkMode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color:
            isDarkMode
                ? const Color(0xFF1E293B).withValues(alpha: 0.8)
                : Colors.grey[100]?.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border(
          right: BorderSide(color: const Color(0xFF6366F1), width: 3),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الرد
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.reply_rounded,
              color: Color(0xFF6366F1),
              size: 16,
            ),
          ),

          const SizedBox(width: 12),

          // محتوى الرد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'رد على ${replyToMessage.senderName}',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF6366F1),
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    if (replyToMessage.replyTypeIcon.isNotEmpty) ...[
                      Text(
                        replyToMessage.replyTypeIcon,
                        style: const TextStyle(fontSize: 12),
                      ),
                      const SizedBox(width: 4),
                    ],
                    Expanded(
                      child: Text(
                        replyToMessage.replyPreview,
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color:
                              isDarkMode ? Colors.grey[300] : Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // زر الإلغاء
          if (onCancel != null)
            GestureDetector(
              onTap: onCancel,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.grey[400]?.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.close_rounded,
                  size: 16,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
