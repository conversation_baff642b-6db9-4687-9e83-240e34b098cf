@echo off
echo ========================================
echo    تحسين وبناء APK محسن الحجم
echo ========================================
echo.

echo 1. تنظيف المشروع...
call flutter clean
echo   ✅ تم تنظيف المشروع

echo.
echo 2. تحديث التبعيات...
call flutter pub get
echo   ✅ تم تحديث التبعيات

echo.
echo 3. بناء APK محسن مع تقسيم ABI...
call flutter build apk --release --split-per-abi --tree-shake-icons --shrink
echo   ✅ تم بناء APK محسن

echo.
echo 4. بناء APK عادي للمقارنة...
call flutter build apk --release --tree-shake-icons
echo   ✅ تم بناء APK عادي

echo.
echo ========================================
echo           تم الانتهاء بنجاح!
echo ========================================
echo.
echo الملفات المنتجة:
echo - APK محسن (مقسم): build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
echo - APK عادي: build\app\outputs\flutter-apk\app-release.apk
echo.
echo لعرض أحجام الملفات:
dir /s build\app\outputs\flutter-apk\*.apk
echo.
pause
