import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import '../providers/theme_provider.dart';

/// Widget عرض الإشعار الواحد
class NotificationWidget extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Dismissible(
          key: Key(notification.id),
          direction: DismissDirection.endToStart,
          onDismissed: (_) => onDismiss?.call(),
          background: Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.delete, color: Colors.white, size: 24),
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            decoration: BoxDecoration(
              color:
                  notification.isRead
                      ? (themeProvider.isDarkMode
                          ? Colors.grey[800]
                          : Colors.grey[100])
                      : (themeProvider.isDarkMode
                          ? const Color(0xFF1E293B)
                          : Colors.white),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    notification.isRead
                        ? Colors.transparent
                        : const Color(0xFF10B981),
                width: notification.isRead ? 0 : 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // أيقونة الإشعار
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Color(
                            int.parse(
                                  notification.colorHex.substring(1),
                                  radix: 16,
                                ) +
                                0xFF000000,
                          ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Center(
                          child: Text(
                            notification.iconPath,
                            style: const TextStyle(fontSize: 20),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),

                      // محتوى الإشعار
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // العنوان
                            Text(
                              notification.title,
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.white
                                        : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 4),

                            // النص
                            Text(
                              notification.body,
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.grey[300]
                                        : Colors.grey[600],
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // الوقت
                            Text(
                              notification.timeAgo,
                              style: GoogleFonts.cairo(
                                fontSize: 12,
                                color:
                                    themeProvider.isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ),

                      // مؤشر عدم القراءة
                      if (!notification.isRead)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(0xFF10B981),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget قائمة الإشعارات
class NotificationsList extends StatelessWidget {
  final List<NotificationModel> notifications;
  final bool isLoading;
  final VoidCallback? onRefresh;

  const NotificationsList({
    super.key,
    required this.notifications,
    this.isLoading = false,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        if (isLoading && notifications.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF10B981)),
            ),
          );
        }

        if (notifications.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_none,
                  size: 64,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[600]
                          : Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد إشعارات',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر إشعاراتك هنا عند وصولها',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color:
                        themeProvider.isDarkMode
                            ? Colors.grey[500]
                            : Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            onRefresh?.call();
          },
          color: const Color(0xFF10B981),
          child: ListView.builder(
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return NotificationWidget(
                notification: notification,
                onTap: () => _handleNotificationTap(context, notification),
                onDismiss: () => _handleNotificationDismiss(notification),
              );
            },
          ),
        );
      },
    );
  }

  void _handleNotificationTap(
    BuildContext context,
    NotificationModel notification,
  ) {
    // وضع علامة مقروء (مبسط)
    if (!notification.isRead) {
      // يمكن إضافة منطق وضع علامة مقروء هنا لاحقاً
    }

    // التنقل حسب نوع الإشعار
    switch (notification.type) {
      case NotificationType.like:
      case NotificationType.comment:
      case NotificationType.share:
      case NotificationType.newPost:
        // التنقل إلى المنشور
        final postId = notification.data['postId'];
        if (postId != null) {
          // Navigator.pushNamed(context, '/post', arguments: postId);
        }
        break;
      case NotificationType.newFile:
        // التنقل إلى الملف أو صفحة الملفات
        final fileUrl = notification.data['fileUrl'];
        if (fileUrl != null) {
          // يمكن إضافة منطق فتح الملف هنا
        }
        break;
      case NotificationType.general:
        // التنقل إلى الرابط المحدد
        if (notification.actionUrl != null) {
          // Navigator.pushNamed(context, notification.actionUrl!);
        }
        break;
      case NotificationType.system:
        // التنقل إلى الإعدادات
        // Navigator.pushNamed(context, '/settings');
        break;
    }
  }

  void _handleNotificationDismiss(NotificationModel notification) {
    // يمكن إضافة منطق حذف الإشعار هنا لاحقاً
  }
}

/// Widget عداد الإشعارات
class NotificationBadge extends StatelessWidget {
  final int count;
  final Widget child;

  const NotificationBadge({
    super.key,
    required this.count,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (count > 0)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
              child: Text(
                count > 99 ? '99+' : count.toString(),
                style: GoogleFonts.cairo(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}

/// Widget أيقونة الإشعارات مع العداد
class NotificationIcon extends StatelessWidget {
  final VoidCallback? onTap;

  const NotificationIcon({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int>(
      stream: NotificationService.getUnreadNotificationsCount(),
      initialData: 0,
      builder: (context, snapshot) {
        final unreadCount = snapshot.data ?? 0;

        return NotificationBadge(
          count: unreadCount,
          child: IconButton(
            onPressed: onTap,
            icon: const Icon(Icons.notifications, color: Color(0xFF10B981)),
          ),
        );
      },
    );
  }
}
